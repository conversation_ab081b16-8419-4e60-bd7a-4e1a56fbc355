"""
Validation API Tests
"""

import unittest
import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from backend.app import create_app


class TestValidationAPI(unittest.TestCase):
    """Test cases for the Validation API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """Clean up after tests."""
        self.app_context.pop()
    
    def test_smacna_validation_success(self):
        """Test successful SMACNA validation."""
        data = {
            'duct_type': 'rectangular',
            'velocity': 1500,
            'friction_rate': 0.08,
            'airflow': 1000,
            'dimensions': {
                'width': 12,
                'height': 8
            }
        }
        
        response = self.client.post(
            '/api/validation/smacna',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertIn('validation', result)
        self.assertIn('compliant', result['validation'])
        self.assertIn('checks', result['validation'])
        self.assertEqual(result['validation']['standard'], 'SMACNA')
    
    def test_smacna_validation_no_data(self):
        """Test SMACNA validation with no data."""
        response = self.client.post(
            '/api/validation/smacna',
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_nfpa_validation_grease_duct(self):
        """Test NFPA validation for grease ducts."""
        data = {
            'duct_type': 'grease',
            'velocity': 1800,
            'slope': 0.25,
            'length': 15
        }
        
        response = self.client.post(
            '/api/validation/nfpa',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertIn('validation', result)
        self.assertEqual(result['validation']['standard'], 'NFPA 96')
        self.assertIn('checks', result['validation'])
    
    def test_ashrae_validation_comfort(self):
        """Test ASHRAE comfort validation."""
        data = {
            'velocity': 800,
            'location': 'occupied',
            'room_type': 'office',
            'duct_type': 'supply'
        }
        
        response = self.client.post(
            '/api/validation/ashrae',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertIn('validation', result)
        self.assertEqual(result['validation']['standard'], 'ASHRAE')
        self.assertIn('checks', result['validation'])
    
    def test_units_validation_imperial(self):
        """Test units validation for imperial units."""
        data = {
            'values': {
                'velocity': 1500,
                'airflow': 1000,
                'pressure': 0.5
            },
            'from_units': 'imperial'
        }
        
        response = self.client.post(
            '/api/validation/units',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertIn('validation', result)
        self.assertIn('valid_units', result['validation'])
        self.assertIn('supported_units', result['validation'])
    
    def test_units_conversion(self):
        """Test units conversion functionality."""
        data = {
            'values': {
                'velocity': 1500,
                'airflow': 1000
            },
            'from_units': 'imperial',
            'to_units': 'metric'
        }
        
        response = self.client.post(
            '/api/validation/units',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertIn('validation', result)
        self.assertIn('conversions', result['validation'])
        
        # Check that conversions were performed
        if result['validation']['conversions']:
            self.assertIn('velocity', result['validation']['conversions'])
    
    def test_validation_error_handling(self):
        """Test validation API error handling."""
        # Test with invalid JSON
        response = self.client.post(
            '/api/validation/smacna',
            data='invalid json',
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
    
    def test_velocity_limits_check(self):
        """Test velocity limits validation."""
        # Test with high velocity
        data = {
            'duct_type': 'rectangular',
            'velocity': 3500,  # Too high
            'friction_rate': 0.08,
            'airflow': 2000
        }
        
        response = self.client.post(
            '/api/validation/smacna',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        # Should have velocity check that fails
        velocity_check = None
        for check in result['validation']['checks']:
            if check['parameter'] == 'velocity':
                velocity_check = check
                break
        
        self.assertIsNotNone(velocity_check)
        self.assertFalse(velocity_check['passed'])
    
    def test_aspect_ratio_check(self):
        """Test rectangular duct aspect ratio validation."""
        data = {
            'duct_type': 'rectangular',
            'velocity': 1500,
            'friction_rate': 0.08,
            'airflow': 1000,
            'dimensions': {
                'width': 24,
                'height': 4  # Aspect ratio = 6:1 (too high)
            }
        }
        
        response = self.client.post(
            '/api/validation/smacna',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        # Should have aspect ratio check that fails
        aspect_check = None
        for check in result['validation']['checks']:
            if check['parameter'] == 'aspect_ratio':
                aspect_check = check
                break
        
        self.assertIsNotNone(aspect_check)
        self.assertFalse(aspect_check['passed'])


if __name__ == '__main__':
    unittest.main()
