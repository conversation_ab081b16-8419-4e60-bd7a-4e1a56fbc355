"""
Authentication API Tests
"""

import unittest
import json
import sys
import os
import tempfile

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from backend.app import create_app


class TestAuthAPI(unittest.TestCase):
    """Test cases for the Authentication API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Test user data
        self.test_user = {
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'name': 'Test User',
            'company': 'Test Company'
        }
    
    def tearDown(self):
        """Clean up after tests."""
        self.app_context.pop()
        
        # Clean up test user file
        try:
            from backend.api.auth import USERS_FILE
            if os.path.exists(USERS_FILE):
                os.remove(USERS_FILE)
        except:
            pass
    
    def test_user_registration_success(self):
        """Test successful user registration."""
        response = self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        result = json.loads(response.data)
        
        self.assertTrue(result['success'])
        self.assertIn('user', result)
        self.assertIn('token', result)
        self.assertEqual(result['user']['email'], self.test_user['email'])
        self.assertEqual(result['user']['name'], self.test_user['name'])
        self.assertNotIn('password_hash', result['user'])
    
    def test_user_registration_missing_data(self):
        """Test registration with missing required data."""
        incomplete_data = {
            'email': '<EMAIL>'
            # Missing password and name
        }
        
        response = self.client.post(
            '/api/auth/register',
            data=json.dumps(incomplete_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_user_registration_weak_password(self):
        """Test registration with weak password."""
        weak_password_data = self.test_user.copy()
        weak_password_data['password'] = '123'  # Too short
        
        response = self.client.post(
            '/api/auth/register',
            data=json.dumps(weak_password_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.data)
        self.assertIn('error', result)
        self.assertIn('8 characters', result['error'])
    
    def test_user_registration_invalid_email(self):
        """Test registration with invalid email."""
        invalid_email_data = self.test_user.copy()
        invalid_email_data['email'] = 'invalid-email'
        
        response = self.client.post(
            '/api/auth/register',
            data=json.dumps(invalid_email_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.data)
        self.assertIn('error', result)
        self.assertIn('email', result['error'].lower())
    
    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email."""
        # Register user first time
        self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        # Try to register again with same email
        response = self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 409)
        result = json.loads(response.data)
        self.assertIn('error', result)
        self.assertIn('already exists', result['error'])
    
    def test_user_login_success(self):
        """Test successful user login."""
        # Register user first
        self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        # Login
        login_data = {
            'email': self.test_user['email'],
            'password': self.test_user['password']
        }
        
        response = self.client.post(
            '/api/auth/login',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertTrue(result['success'])
        self.assertIn('user', result)
        self.assertIn('token', result)
        self.assertEqual(result['user']['email'], self.test_user['email'])
    
    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        # Register user first
        self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        # Try login with wrong password
        login_data = {
            'email': self.test_user['email'],
            'password': 'wrongpassword'
        }
        
        response = self.client.post(
            '/api/auth/login',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 401)
        result = json.loads(response.data)
        self.assertIn('error', result)
        self.assertIn('credentials', result['error'])
    
    def test_user_login_nonexistent_user(self):
        """Test login with non-existent user."""
        login_data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        
        response = self.client.post(
            '/api/auth/login',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 401)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_token_verification_success(self):
        """Test successful token verification."""
        # Register and get token
        register_response = self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        register_result = json.loads(register_response.data)
        token = register_result['token']
        
        # Verify token
        verify_data = {'token': token}
        response = self.client.post(
            '/api/auth/verify',
            data=json.dumps(verify_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertTrue(result['success'])
        self.assertTrue(result['valid'])
        self.assertIn('user', result)
    
    def test_token_verification_invalid_token(self):
        """Test token verification with invalid token."""
        verify_data = {'token': 'invalid.token.here'}
        
        response = self.client.post(
            '/api/auth/verify',
            data=json.dumps(verify_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 401)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_protected_endpoint_with_valid_token(self):
        """Test accessing protected endpoint with valid token."""
        # Register and get token
        register_response = self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        register_result = json.loads(register_response.data)
        token = register_result['token']
        
        # Access protected profile endpoint
        response = self.client.get(
            '/api/auth/profile',
            headers={'Authorization': f'Bearer {token}'}
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertTrue(result['success'])
        self.assertIn('user', result)
    
    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without token."""
        response = self.client.get('/api/auth/profile')
        
        self.assertEqual(response.status_code, 401)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_protected_endpoint_with_invalid_token(self):
        """Test accessing protected endpoint with invalid token."""
        response = self.client.get(
            '/api/auth/profile',
            headers={'Authorization': 'Bearer invalid.token.here'}
        )
        
        self.assertEqual(response.status_code, 401)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_logout_success(self):
        """Test successful logout."""
        # Register and get token
        register_response = self.client.post(
            '/api/auth/register',
            data=json.dumps(self.test_user),
            content_type='application/json'
        )
        
        register_result = json.loads(register_response.data)
        token = register_result['token']
        
        # Logout
        response = self.client.post(
            '/api/auth/logout',
            headers={'Authorization': f'Bearer {token}'}
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['success'])


if __name__ == '__main__':
    unittest.main()
