"""
Exports API Tests
"""

import unittest
import json
import sys
import os
import tempfile

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from backend.app import create_app


class TestExportsAPI(unittest.TestCase):
    """Test cases for the Exports API endpoints."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Sample calculation data for testing
        self.sample_data = {
            'project_name': 'Test Project',
            'calculations': {
                'calc_1': {
                    'name': 'Main Supply Duct',
                    'input': {
                        'airflow': 1000,
                        'duct_type': 'rectangular',
                        'friction_rate': 0.08
                    },
                    'results': {
                        'duct_size': {
                            'value': '12x8',
                            'unit': 'inches'
                        },
                        'velocity': {
                            'value': 1500,
                            'unit': 'FPM'
                        },
                        'area': {
                            'value': 0.67,
                            'unit': 'sq ft'
                        }
                    }
                }
            },
            'template': 'standard'
        }
    
    def tearDown(self):
        """Clean up after tests."""
        self.app_context.pop()
    
    def test_pdf_export_success(self):
        """Test successful PDF export."""
        response = self.client.post(
            '/api/exports/pdf',
            data=json.dumps(self.sample_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertEqual(result['status'], 'success')
        self.assertIn('filename', result)
        self.assertIn('download_url', result)
        self.assertIn('file_size', result)
        self.assertTrue(result['filename'].endswith('.pdf'))
    
    def test_excel_export_success(self):
        """Test successful Excel export."""
        response = self.client.post(
            '/api/exports/excel',
            data=json.dumps(self.sample_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertEqual(result['status'], 'success')
        self.assertIn('filename', result)
        self.assertIn('download_url', result)
        self.assertIn('file_size', result)
        self.assertTrue(result['filename'].endswith('.xlsx'))
    
    def test_csv_export_success(self):
        """Test successful CSV export."""
        response = self.client.post(
            '/api/exports/csv',
            data=json.dumps(self.sample_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertEqual(result['status'], 'success')
        self.assertIn('filename', result)
        self.assertIn('download_url', result)
        self.assertIn('file_size', result)
        self.assertTrue(result['filename'].endswith('.csv'))
    
    def test_export_no_data(self):
        """Test export with no data."""
        response = self.client.post(
            '/api/exports/pdf',
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.data)
        self.assertIn('error', result)
    
    def test_export_empty_calculations(self):
        """Test export with empty calculations."""
        data = {
            'project_name': 'Empty Project',
            'calculations': {},
            'template': 'standard'
        }
        
        response = self.client.post(
            '/api/exports/pdf',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertEqual(result['status'], 'success')
    
    def test_export_different_templates(self):
        """Test export with different templates."""
        templates = ['standard', 'detailed', 'summary']
        
        for template in templates:
            data = self.sample_data.copy()
            data['template'] = template
            
            response = self.client.post(
                '/api/exports/pdf',
                data=json.dumps(data),
                content_type='application/json'
            )
            
            self.assertEqual(response.status_code, 200)
            result = json.loads(response.data)
            self.assertEqual(result['template'], template)
    
    def test_export_formats_endpoint(self):
        """Test the export formats endpoint."""
        response = self.client.get('/api/exports/formats')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertIn('available_formats', result)
        self.assertIn('default_format', result)
        
        formats = result['available_formats']
        self.assertIn('pdf', formats)
        self.assertIn('excel', formats)
        self.assertIn('csv', formats)
    
    def test_filename_generation(self):
        """Test that filenames are generated correctly."""
        data = {
            'project_name': 'Test Project With Spaces',
            'calculations': self.sample_data['calculations'],
            'template': 'standard'
        }
        
        response = self.client.post(
            '/api/exports/pdf',
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        # Filename should replace spaces with underscores
        self.assertIn('Test_Project_With_Spaces', result['filename'])
        self.assertTrue(result['filename'].endswith('.pdf'))
    
    def test_complex_calculation_data(self):
        """Test export with complex calculation data."""
        complex_data = {
            'project_name': 'Complex Project',
            'calculations': {
                'calc_1': {
                    'name': 'Supply Duct',
                    'input': {
                        'airflow': 1000,
                        'duct_type': 'rectangular',
                        'friction_rate': 0.08,
                        'dimensions': {
                            'width': 12,
                            'height': 8
                        }
                    },
                    'results': {
                        'duct_size': {'value': '12x8', 'unit': 'inches'},
                        'velocity': {'value': 1500, 'unit': 'FPM'},
                        'area': {'value': 0.67, 'unit': 'sq ft'},
                        'pressure_loss': {'value': 0.08, 'unit': 'in. w.g.'}
                    }
                },
                'calc_2': {
                    'name': 'Return Duct',
                    'input': {
                        'airflow': 800,
                        'duct_type': 'round',
                        'friction_rate': 0.06
                    },
                    'results': {
                        'diameter': {'value': 14, 'unit': 'inches'},
                        'velocity': {'value': 1200, 'unit': 'FPM'},
                        'area': {'value': 1.07, 'unit': 'sq ft'}
                    }
                }
            },
            'template': 'detailed'
        }
        
        response = self.client.post(
            '/api/exports/excel',
            data=json.dumps(complex_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertEqual(result['status'], 'success')
    
    def test_cleanup_endpoint(self):
        """Test the cleanup endpoint."""
        response = self.client.post('/api/exports/cleanup')
        
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        
        self.assertEqual(result['status'], 'success')
        self.assertIn('cleaned_files', result)
        self.assertIn('message', result)


if __name__ == '__main__':
    unittest.main()
