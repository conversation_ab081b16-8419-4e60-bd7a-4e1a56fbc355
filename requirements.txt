# Core Flask framework
Flask==2.3.3
Flask-CORS==6.0.0

# API and data handling
jsonschema==4.19.1
pydantic==2.4.2
marshmallow==3.20.1

# Authentication and security
PyJWT==2.8.0
bcrypt==4.0.1

# Mathematical calculations
numpy==1.24.3
scipy==1.11.3
pandas==2.0.3

# Unit conversion and validation
pint==0.22
cerberus==1.3.5

# Export functionality
openpyxl==3.1.2
reportlab==4.0.4
jinja2==3.1.6

# Development and testing
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
black==24.3.0
flake8==6.1.0
mypy==1.6.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Environment and configuration
python-dotenv==1.0.0
click==8.1.7

# Logging and monitoring
structlog==23.1.0

# Date and time handling
python-dateutil==2.8.2

# HTTP client for testing
requests==2.32.4
