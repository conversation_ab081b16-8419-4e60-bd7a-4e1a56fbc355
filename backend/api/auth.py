"""
Authentication API Blueprint

Handles user authentication, registration, and session management.
"""

from flask import Blueprint, request, jsonify, current_app
import structlog
import jwt
import bcrypt
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from functools import wraps

logger = structlog.get_logger()

auth_bp = Blueprint('auth', __name__)

# Constants
TOKEN_EXPIRY_HOURS = 24
REFRESH_TOKEN_EXPIRY_DAYS = 30
MIN_PASSWORD_LENGTH = 8

# Simple in-memory user store for development (replace with database in production)
USERS_FILE = os.path.join(os.path.dirname(__file__), '..', 'data', 'users.json')
USERS_DATA = {}

def _load_users():
    """Load users from JSON file."""
    global USERS_DATA
    try:
        if os.path.exists(USERS_FILE):
            with open(USERS_FILE, 'r') as f:
                USERS_DATA = json.load(f)
        else:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(USERS_FILE), exist_ok=True)
            USERS_DATA = {}
    except Exception as e:
        logger.warning("Failed to load users file", error=str(e))
        USERS_DATA = {}

def _save_users():
    """Save users to JSON file."""
    try:
        with open(USERS_FILE, 'w') as f:
            json.dump(USERS_DATA, f, indent=2)
    except Exception as e:
        logger.error("Failed to save users file", error=str(e))

def _hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def _verify_password(password: str, hashed: str) -> bool:
    """Verify a password against its hash."""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def _generate_token(user_id: str, email: str) -> str:
    """Generate a JWT token for the user."""
    payload = {
        'user_id': user_id,
        'email': email,
        'exp': datetime.utcnow() + timedelta(hours=TOKEN_EXPIRY_HOURS),
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, current_app.config['SECRET_KEY'], algorithm='HS256')

def _verify_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(f):
    """Decorator to require authentication for API endpoints."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]  # Bearer <token>
            except IndexError:
                return jsonify({'error': 'Invalid authorization header format'}), 401
        
        if not token:
            return jsonify({'error': 'Authentication token required'}), 401
        
        payload = _verify_token(token)
        if not payload:
            return jsonify({'error': 'Invalid or expired token'}), 401
        
        # Add user info to request context
        request.current_user = payload
        return f(*args, **kwargs)
    
    return decorated_function

# Load users on module import
_load_users()

@auth_bp.route('/register', methods=['POST'])
def register():
    """
    Register a new user.
    
    Expected input:
    {
        "email": str,
        "password": str,
        "name": str,
        "company": str (optional)
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        name = data.get('name', '').strip()
        company = data.get('company', '').strip()
        
        # Validation
        if not email or not password or not name:
            return jsonify({'error': 'Email, password, and name are required'}), 400
        
        if len(password) < MIN_PASSWORD_LENGTH:
            return jsonify({'error': f'Password must be at least {MIN_PASSWORD_LENGTH} characters'}), 400
        
        if '@' not in email:
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Check if user already exists
        if email in USERS_DATA:
            return jsonify({'error': 'User already exists'}), 409
        
        # Create new user
        user_id = f"user_{len(USERS_DATA) + 1}"
        hashed_password = _hash_password(password)
        
        user_data = {
            'id': user_id,
            'email': email,
            'name': name,
            'company': company,
            'password_hash': hashed_password,
            'tier': 'free',  # Default tier
            'created_at': datetime.utcnow().isoformat(),
            'last_login': None,
            'is_active': True
        }
        
        USERS_DATA[email] = user_data
        _save_users()
        
        # Generate token
        token = _generate_token(user_id, email)
        
        # Return user data (without password hash)
        user_response = {k: v for k, v in user_data.items() if k != 'password_hash'}
        
        result = {
            'success': True,
            'message': 'User registered successfully',
            'user': user_response,
            'token': token
        }
        
        logger.info("User registered", email=email, user_id=user_id)
        return jsonify(result), 201
        
    except Exception as e:
        logger.error("Registration failed", error=str(e))
        return jsonify({'error': 'Registration failed', 'message': str(e)}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """
    Authenticate a user and return a token.
    
    Expected input:
    {
        "email": str,
        "password": str
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Find user
        user_data = USERS_DATA.get(email)
        if not user_data:
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Verify password
        if not _verify_password(password, user_data['password_hash']):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Check if user is active
        if not user_data.get('is_active', True):
            return jsonify({'error': 'Account is deactivated'}), 401
        
        # Update last login
        user_data['last_login'] = datetime.utcnow().isoformat()
        _save_users()
        
        # Generate token
        token = _generate_token(user_data['id'], email)
        
        # Return user data (without password hash)
        user_response = {k: v for k, v in user_data.items() if k != 'password_hash'}
        
        result = {
            'success': True,
            'message': 'Login successful',
            'user': user_response,
            'token': token
        }
        
        logger.info("User logged in", email=email, user_id=user_data['id'])
        return jsonify(result)
        
    except Exception as e:
        logger.error("Login failed", error=str(e))
        return jsonify({'error': 'Login failed', 'message': str(e)}), 500

@auth_bp.route('/verify', methods=['POST'])
def verify_token():
    """
    Verify a JWT token and return user information.
    
    Expected input:
    {
        "token": str
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        token = data.get('token')
        if not token:
            return jsonify({'error': 'Token is required'}), 400
        
        payload = _verify_token(token)
        if not payload:
            return jsonify({'error': 'Invalid or expired token'}), 401
        
        # Get user data
        email = payload.get('email')
        user_data = USERS_DATA.get(email)
        
        if not user_data:
            return jsonify({'error': 'User not found'}), 404
        
        # Return user data (without password hash)
        user_response = {k: v for k, v in user_data.items() if k != 'password_hash'}
        
        result = {
            'success': True,
            'valid': True,
            'user': user_response
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error("Token verification failed", error=str(e))
        return jsonify({'error': 'Verification failed', 'message': str(e)}), 500

@auth_bp.route('/logout', methods=['POST'])
@require_auth
def logout():
    """
    Logout a user (token-based, so just confirm the action).
    """
    try:
        user_email = request.current_user.get('email')
        
        result = {
            'success': True,
            'message': 'Logout successful'
        }
        
        logger.info("User logged out", email=user_email)
        return jsonify(result)
        
    except Exception as e:
        logger.error("Logout failed", error=str(e))
        return jsonify({'error': 'Logout failed', 'message': str(e)}), 500

@auth_bp.route('/profile', methods=['GET'])
@require_auth
def get_profile():
    """Get current user profile."""
    try:
        email = request.current_user.get('email')
        user_data = USERS_DATA.get(email)
        
        if not user_data:
            return jsonify({'error': 'User not found'}), 404
        
        # Return user data (without password hash)
        user_response = {k: v for k, v in user_data.items() if k != 'password_hash'}
        
        return jsonify({
            'success': True,
            'user': user_response
        })
        
    except Exception as e:
        logger.error("Profile retrieval failed", error=str(e))
        return jsonify({'error': 'Profile retrieval failed', 'message': str(e)}), 500
